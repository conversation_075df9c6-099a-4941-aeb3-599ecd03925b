build_prod:
  variables:
    GIT_STRATEGY: none
  stage: deploy
  only:
    - master@cmminear/patriciabanwellmassage.com
  script:
    - pwd
    - cd /home/<USER>/patriciabanwellmassage.com
    - git pull origin master
  tags:
    - dreamhost
build_test:
  variables:
    GIT_STRATEGY: none
  stage: test
  only:
    - test@cmminear/patriciabanwellmassage.com
  script:
    - cd /home/<USER>/test.patriciabanwellmassage.com
    - git pull origin test
  tags:
    - dreamhost