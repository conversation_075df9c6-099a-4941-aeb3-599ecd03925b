#!/bin/bash

echo "🚀 Starting <PERSON> Massage Website..."
echo "Building and starting Docker container..."

# Build and start the container
docker compose up --build -d

# Wait a moment for the container to start
sleep 3

# Check if container is running
if docker compose ps | grep -q "Up"; then
    echo "✅ Website is now running!"
    echo ""
    echo "🌐 Access the website at: http://localhost:8080"
    echo ""
    echo "📱 To test mobile view, you can:"
    echo "   - Use browser dev tools (F12 → Device toolbar)"
    echo "   - Access from your phone using your computer's IP"
    echo ""
    echo "🛑 To stop the website, run: ./stop-website.sh"
    echo "   or use: docker compose down"
else
    echo "❌ Failed to start the website. Check the logs:"
    docker compose logs
fi
