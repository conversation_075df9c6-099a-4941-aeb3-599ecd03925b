<?php
	//Start session
	session_start();

	//Sanitize the POST values
	$emailer = $_POST['email'];
	$name = $_POST['name'];
	$comments = $_POST['comments'];

if ($emailer == '')
{
	$message = "Please enter a valid email";

		$jsonValue = array('success' => false, 'message' => $message);
		echo json_encode($jsonValue);

		 	exit();
}
if ($name == '')
{
	$message = "Please enter a valid name";

		$jsonValue = array('success' => false, 'message' => $message);
		echo json_encode($jsonValue);

		 	exit();
}

$email = "<EMAIL>";

$subject = "Message from a visitor on your website!";
$message = "Message: $comments \n From: $emailer \n Name: $name";

$from = "<EMAIL>";
$headers = "From:" . $from;

mail($email,$subject,$message,$headers);


$message = "Your message has been received! I will respond to you as soon as I can. Thank you!";



$jsonValue = array('success' => true, 'message' => $message);
		echo json_encode($jsonValue);
exit();



?>